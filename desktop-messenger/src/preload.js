const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  // Storage
  getStoredData: (key) => ipcRenderer.invoke('get-stored-data', key),
  setStoredData: (key, value) => ipc<PERSON><PERSON>er.invoke('set-stored-data', key, value),
  deleteStoredData: (key) => ipc<PERSON><PERSON><PERSON>.invoke('delete-stored-data', key),

  // Notifications
  showNotification: (options) => ipcRenderer.invoke('show-notification', options),
  setBadgeCount: (count) => ipcRenderer.invoke('set-badge-count', count),

  // External URLs
  openExternalUrl: (url) => ipc<PERSON>enderer.invoke('open-external-url', url),

  // Platform info
  platform: process.platform
});